{"name": "memici-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@fastify/compress": "^8.0.3", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/rate-limit": "^10.3.0", "@fastify/reply-from": "^12.1.0", "@fastify/static": "^8.2.0", "@keyv/redis": "^4.5.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/swagger": "^11.2.0", "apple-signin-auth": "^2.0.0", "bcrypt": "^6.0.0", "cache-manager": "^7.0.1", "class-validator": "^0.14.2", "fastify": "5.3.3", "joi": "^17.13.3", "keyv": "^5.3.4", "mongoose": "^8.16.1", "nestjs-cls": "^6.0.1", "nestjs-zod": "^4.3.1", "nodemailer": "^7.0.4", "openai": "^5.8.2", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-jwt": "^4.0.1", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "pino-telegram-webhook": "^0.3.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "undici": "^7.11.0", "uuid": "^11.1.0", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.7", "@types/jest": "^30.0.0", "@types/node": "^24.0.7", "@types/passport-apple": "^2.0.3", "@types/supertest": "^6.0.3", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.2.0", "jest": "^30.0.3", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}