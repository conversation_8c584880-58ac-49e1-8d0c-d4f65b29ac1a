const REPO = "**************:memici/memici-api.git";
const TARGET_SERVER_HOST = process.env.TARGET_SERVER_HOST ? process.env.TARGET_SERVER_HOST.trim() : '';
const TARGET_SERVER_USER = process.env.TARGET_SERVER_USER ? process.env.TARGET_SERVER_USER.trim() : '';
const TARGET_SERVER_APP_PATH = `/home/<USER>/apps/memici-api`;
const BRANCH = process.env.CI_COMMIT_REF_NAME || 'main';

module.exports = {
  apps: [
    {
      name: "memici-api",
      script: './dist/main.js',
      args: [ "run", "start:prod"],
      exec_mode: "fork_mode",
      max_memory_restart: "500M",
      max_restarts: "10",
      min_uptime: "5000",
      kill_timeout: "1000",
      wait_ready: true
    }
  ],
  deploy: {
    production: {
      user: TARGET_SERVER_USER,
      host: [
        TARGET_SERVER_HOST
      ],
      ref: `origin/${BRANCH}`,
      repo: REPO,
      ssh_options: ['StrictHostKeyChecking=no', 'PasswordAuthentication=no'],
      path: TARGET_SERVER_APP_PATH,
      "post-deploy": "npm ci && npm run build && pm2 startOrReload ecosystem.config.cjs"
    }
  }
};
