{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "NestJS Debug",
      "runtimeExecutable": "nest",
      "runtimeArgs": ["start", "--debug", "--watch"],
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      },
      "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"],
      "skipFiles": ["<node_internals>/**"],
      "outFiles": ["${workspaceFolder}/dist/**/*.js"]
    },
  ]
}