import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { hash } from 'bcrypt';
import { v4 as uuid } from 'uuid';

export type UserDocument = User & Document;

class UserName {
  @Prop({ trim: true })
  givenName?: string;

  @Prop({ trim: true })
  familyName?: string;

  @Prop({ trim: true })
  middleName?: string;

  @Prop({ trim: true, required: true })
  displayName: string;

  @Prop({ trim: true, lowercase: true })
  locale?: string;
}

export enum Role {
  User = 'user',
  Admin = 'admin',
}

@Schema({
  timestamps: true,
  toJSON: {
    transform: (_, ret) => {
      delete ret.__v;
      delete ret._id;
      return ret;
    },
  },
})
export class User {
  @Prop({ type: String, unique: true, required: true, default: () => uuid() })
  externalId: string;

  @Prop({ required: true, unique: true, lowercase: true })
  email: string;

  @Prop({ type: UserName, _id: false, required: true })
  name: UserName;

  @Prop()
  picture?: string;

  @Prop({
    type: [
      {
        provider: { type: String, required: true },
        providerId: { type: String, required: true },
        email: { type: String },
        name: {
          givenName: String,
          familyName: String,
          displayName: String,
        },
        picture: String,
      },
    ],
    default: [],
  })
  socialAccounts: {
    provider: string;
    providerId: string;
    email: string;
    name?: {
      givenName?: string;
      familyName?: string;
      displayName: string;
    };
    picture?: string;
  }[];

  @Prop({ select: false })
  passwordHash?: string;

  @Prop({ default: false })
  emailVerified: boolean;

  @Prop({
    type: [String],
    enum: Object.values(Role),
    default: [Role.User],
  })
  roles: Role[];

  @Prop({ type: [String], default: [] })
  permissions: string[];

  @Prop({ type: Date, default: null })
  deletedAt: Date | null;

  @Prop({ default: true })
  isActive: boolean;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.pre<UserDocument>('save', async function (next) {
  if (this.isModified('passwordHash')) {
    const saltRounds = 12;

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
    this.passwordHash = await hash(this.passwordHash!, saltRounds);
  }
  next();
});

UserSchema.index(
  { 'socialAccounts.provider': 1, 'socialAccounts.providerId': 1 },
  { unique: true },
);

UserSchema.index({ deletedAt: 1 });
