import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, MongooseError, UpdateQuery } from 'mongoose';
import { User, UserDocument } from '../schemas/user.schema';
import { UserNotFoundError } from './user.errors';
import { DatabaseError } from '@/common/errors/database.error';
import { UpdateUserDto } from '../users.controller';

export class CreateSocialUserDto {
  provider: string;
  providerId: string;
  email: string;
  name?: string;
  picture?: string;
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
  ) {}

  async findOrCreateSocialUser(
    dto: CreateSocialUserDto,
  ): Promise<UserDocument> {
    const { provider, providerId, email, name, picture } = dto;
    try {
      let user = await this.userModel
        .findOne({
          'socialAccounts.provider': provider,
          'socialAccounts.providerId': providerId,
        })
        .exec();

      if (user) {
        try {
          // Обновляем информацию о социальном аккаунте
          await this.userModel
            .updateOne(
              {
                _id: user._id,
                'socialAccounts.provider': provider,
                'socialAccounts.providerId': providerId,
              },
              {
                $set: {
                  'socialAccounts.$.email': email,
                  'socialAccounts.$.name': name,
                  'socialAccounts.$.picture': picture,
                },
              },
            )
            .exec();

          // Получаем обновленные данные пользователя
          user = await this.userModel.findById(user._id).exec();
          if (!user) {
            throw new UserNotFoundError('User not found after update');
          }

          return user;
        } catch {
          throw new DatabaseError('Failed to update social account');
        }
      }

      if (email) {
        try {
          user = await this.userModel.findOne({ email }).exec();
        } catch {
          throw new UserNotFoundError('Failed to find user');
        }

        if (user) {
          try {
            user.socialAccounts.push({
              provider,
              providerId,
              email,
              name: {
                displayName: name || '',
              },
              picture,
            });
            await user.save();
            return user;
          } catch (error) {
            this.logger.error(`Failed to add social account: ${error}`);
            throw new DatabaseError('Failed to add social account');
          }
        }
      }

      // Создаем нового пользователя
      try {
        const newUser = await this.userModel.create({
          email,
          name: {
            displayName: name || '',
            locale: 'en',
            givenName: name || '',
            familyName: name || '',
            middleName: name || '',
          },
          picture,
          socialAccounts: [
            {
              provider,
              providerId,
              email,
              name: {
                displayName: name || '',
              },
              picture,
            },
          ],
        });

        return newUser;
      } catch {
        throw new DatabaseError('Failed to create user');
      }
    } catch (error) {
      if (error instanceof MongooseError) {
        this.logger.error(`Database error: ${error.message}`);
        throw new DatabaseError('Database operation failed');
      }
      throw error;
    }
  }

  async findOne(externalId: string): Promise<UserDocument> {
    try {
      const user = await this.userModel
        .findOne({ externalId, deletedAt: null })
        .exec();
      if (!user) {
        throw new UserNotFoundError('User not found');
      }
      return user;
    } catch (error) {
      if (error instanceof MongooseError) {
        this.logger.error(`Database error: ${error.message}`, {
          method: 'findOne',
        });
        throw new DatabaseError('Database operation failed');
      }
      throw error;
    }
  }

  async updateProfile(id: string, dto: UpdateUserDto) {
    const set: UpdateQuery<User> = { ...dto };
    if (dto.name) {
      set['name.displayName'] = dto.name;
      set.isProfileComplete = true;
    }

    const updated = await this.userModel
      .findOneAndUpdate(
        {
          externalId: id,
        },
        { $set: set },
        { runValidators: true, upsert: false, context: 'query' },
      )
      .exec();

    return updated;
  }
}
