import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './services/users.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ClsService } from 'nestjs-cls';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @MinLength(2)
  name?: string;
}

@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly cls: ClsService,
  ) {}

  @Get('me')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile',
  })
  async getProfile() {
    const payload = this.cls.get<{ sub: string }>('user');
    if (!payload) {
      throw new NotFoundException('User not found');
    }

    try {
      const user = await this.usersService.findOne(payload.sub);
      return user;
    } catch (error) {
      throw new NotFoundException('User not found', { cause: error });
    }
  }

  @Patch('me')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Update profile',
  })
  async updateProfile(@Body() dto: UpdateUserDto) {
    const payload = this.cls.get<{ sub: string }>('user');
    if (!payload) {
      throw new NotFoundException('User not found');
    }

    try {
      const update = await this.usersService.updateProfile(payload.sub, dto);
      return { data: update, message: 'Success' };
    } catch (error) {
      throw new NotFoundException('User not found', { cause: error });
    }
  }
}
