import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { LocalAuthService } from './services/local-auth.service';
import { AppleAuthController } from './apple/apple-auth.controller';
import { GoogleAuthController } from './google/google-auth.controller';
import { AppleAuthService } from './apple/apple-auth.service';
import { GoogleAuthService } from './google/google-auth.service';
import { AppleVerifyTokenService } from './apple/apple-verify-token.service';
import { GoogleVerifyTokenService } from './google/google-verify-token.service';
import { MagicLinkAuthController } from './magic-link/magic-link.controller';
import { SendLinkService } from './magic-link/magic-link.service';

@Module({
  imports: [
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        secret: config.getOrThrow<string>('JWT_SECRET'),
        signOptions: {
          issuer: 'memici-api',
          expiresIn: config.getOrThrow<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
    }),
  ],
  controllers: [
    AppleAuthController,
    GoogleAuthController,
    MagicLinkAuthController,
  ],
  providers: [
    ConfigService,
    GoogleAuthService,
    GoogleVerifyTokenService,
    AppleVerifyTokenService,
    AppleAuthService,
    LocalAuthService,
    SendLinkService,
  ],
  exports: [JwtModule],
})
export class AuthModule {}
