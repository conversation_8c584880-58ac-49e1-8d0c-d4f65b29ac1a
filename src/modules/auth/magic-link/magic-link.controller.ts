import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { ApiProperty } from '@nestjs/swagger';
import { SendLinkService } from './magic-link.service';
import { IsEmail, IsNumber, IsString } from 'class-validator';

export class MagicLinkAuthDto {
  @ApiProperty()
  @IsEmail()
  @IsString()
  email: string;
}

export class MagicLinkLoginDto {
  @ApiProperty()
  @IsNumber()
  code: number;
}

@ApiTags('Auth')
@Controller('auth')
export class MagicLinkAuthController {
  private readonly logger = new Logger(MagicLinkAuthController.name);
  constructor(private readonly sendLinkService: SendLinkService) {}

  @Post('magic-link/send')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Sign in with Magic Link' })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Successfully sent magic link',
  })
  async sendLink(@Body() { email }: MagicLinkAuthDto) {
    try {
      const code = await this.sendLinkService.sendLink(email);
      return { message: 'Link sent to email', data: { code } };
    } catch (err) {
      return new InternalServerErrorException(err);
    }
  }

  @Post('magic-link')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sign in with Magic Link' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully sent magic link',
  })
  async magicLogin(@Body() { code }: MagicLinkLoginDto): Promise<any> {
    try {
      return await this.sendLinkService.magicLogin(code);
    } catch (err) {
      return new InternalServerErrorException(err);
    }
  }
}
