import { UsersService } from '@/modules/users/services/users.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Cache } from 'cache-manager';
import { v4 } from 'uuid';

@Injectable()
export class SendLinkService {
  private readonly logger = new Logger(SendLinkService.name);

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async sendLink(email: string): Promise<number> {
    const { randomInt } = await import('node:crypto');
    const code = randomInt(100000, 999999);
    await this.cacheManager.set(`magic:${code}`, email, 600000);
    return code;
  }

  async magicLogin(code: number): Promise<any> {
    const email = await this.cacheManager.get<string>(`magic:${code}`);
    if (!email) {
      throw Error('Wrong code');
    }

    const user = await this.usersService.findOrCreateSocialUser({
      provider: 'magic-link',
      providerId: email,
      email: email,
    });

    const accessToken = await this.jwtService.signAsync({
      sub: user.externalId,
      role: user.roles,
      permissions: user.permissions,
      jti: v4(),
    });

    return {
      accessToken,
      user: {
        id: user.externalId,
        email: user.email,
        name: user.name.displayName,
        picture: user.picture,
      },
    };
  }
}
