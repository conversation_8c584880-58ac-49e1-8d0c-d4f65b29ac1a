import { ApiProperty } from '@nestjs/swagger';

export class SocialAuthDto {
  @ApiProperty()
  token: string;

  @ApiProperty()
  data?: any;
}

export class LocalAuthDto {
  @ApiProperty()
  email: string;

  @ApiProperty()
  password: string;
}

export class AuthResponseDto {
  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken?: string;

  @ApiProperty()
  user: {
    id: string;
    email: string;
    name?: string;
  };
}
