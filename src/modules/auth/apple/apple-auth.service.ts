import { Injectable, Logger } from '@nestjs/common';
import { AppleVerifyTokenService } from './apple-verify-token.service';
import { UsersService } from '@/modules/users/services/users.service';
import { JwtService } from '@nestjs/jwt';
import { v4 } from 'uuid';
import { AppleAuthDto } from './apple-auth.controller';

@Injectable()
export class AppleAuthService {
  private readonly logger = new Logger(AppleAuthService.name);

  constructor(
    private readonly appleVerifyService: AppleVerifyTokenService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async loginWithApple(payload: AppleAuthDto): Promise<any> {
    try {
      const applePayload = await this.appleVerifyService.validate(
        payload.token,
      );

      const user = await this.usersService.findOrCreateSocialUser({
        provider: 'apple',
        providerId: applePayload.id,
        email: payload?.email || applePayload.email,
        name: payload?.givenName + ' ' + payload?.familyName || '',
      });

      const accessToken = await this.jwtService.signAsync({
        sub: user.externalId,
        role: user.roles,
        permissions: user.permissions,
        jti: v4(),
      });

      return {
        accessToken,
        user: {
          id: user.externalId,
          email: user.email,
          name: user.name.displayName,
          picture: user.picture,
        },
      };
    } catch (error) {
      this.logger.error('Failed to login with Apple', error);
      throw error;
    }
  }
}
