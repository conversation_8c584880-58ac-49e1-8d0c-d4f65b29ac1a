import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppleAuthService } from '../apple/apple-auth.service';
import { AuthResponseDto } from '../dtos/auth.dto';

import { ApiProperty } from '@nestjs/swagger';

export class AppleAuthDto {
  @ApiProperty()
  token: string;

  @ApiProperty({ required: false })
  email?: string;

  @ApiProperty({ required: false })
  givenName?: string;

  @ApiProperty({ required: false })
  familyName?: string;
}

@ApiTags('Auth')
@Controller('auth')
export class AppleAuthController {
  private readonly logger = new Logger(AppleAuthController.name);
  constructor(private readonly appleAuthService: AppleAuthService) {}

  @Post('apple')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sign in with Apple' })
  @ApiResponse({
    status: HttpStatus.OK,
    type: AuthResponseDto,
    description: 'Successfully authenticated',
  })
  async appleLogin(@Body() body: AppleAuthDto): Promise<any> {
    try {
      return await this.appleAuthService.loginWithApple(body);
    } catch (error) {
      this.logger.error('Failed to login with Apple', error);
      return new InternalServerErrorException('Failed to login with Apple', {
        cause: error,
      });
    }
  }

  @Post('notifications')
  @ApiOperation({ summary: 'Handle Apple ID deletion notifications' })
  @ApiResponse({
    status: 200,
    description: 'Notification processed successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid notification payload' })
  handleAccountDeletion(@Body() payload: { sub: string; type: string }) {
    try {
      this.logger.log(
        `Received Apple account deletion notification for user: ${payload.sub}`,
      );

      if (payload.type === 'account-delete') {
        // TODO: Implement user account deletion logic
        // 1. Find user by Apple ID (sub)
        // 2. Delete or deactivate user account
        // 3. Clean up associated data
      }

      return { status: 'success' };
    } catch (error) {
      this.logger.error(
        'Error processing Apple account deletion notification',
        error,
      );
      throw error;
    }
  }
}
