import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import appleSigninAuth from 'apple-signin-auth';

export interface AppleVerifyTokenPayload {
  id: string;
  email: string;
}

@Injectable()
export class AppleVerifyTokenService {
  private clientId: string;

  constructor(private readonly configService: ConfigService) {
    this.clientId = this.configService.getOrThrow<string>('APPLE_CLIENT_ID');
  }

  async validate(idToken: string): Promise<AppleVerifyTokenPayload> {
    try {
      const payload = await appleSigninAuth.verifyIdToken(idToken, {
        audience: this.clientId,
      });

      return {
        id: payload.sub,
        email: payload.email,
      };
    } catch (error) {
      throw new Error('Failed to validate Apple token', { cause: error });
    }
  }
}
