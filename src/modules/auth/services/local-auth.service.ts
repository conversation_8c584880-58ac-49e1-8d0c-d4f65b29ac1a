import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { UsersService } from '@/modules/users/services/users.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class LocalAuthService {
  private readonly logger = new Logger(LocalAuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async validate(email: string, password: string): Promise<any> {
    try {
      const user: any = await this.usersService.findOne(email);
      if (!user) {
        throw new UnauthorizedException('Invalid email or password');
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      if (user.password !== password) {
        throw new UnauthorizedException('Invalid email or password');
      }

      const accessToken = this.jwtService.sign({
        sub: '',
      });

      return {
        accessToken,
        user: {
          id: '',
          email: '',
          name: '',
          picture: '',
        },
      };
    } catch {
      throw new UnauthorizedException('Failed to validate Google token');
    }
  }
}
