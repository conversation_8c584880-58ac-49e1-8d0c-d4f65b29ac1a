import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface GoogleVerifyTokenPayload {
  iss: string;
  azp: string;
  aud: string;
  sub: string;
  email: string;
  email_verified: boolean;
  name?: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
  locale?: string;
  iat: number;
  exp: number;
}

export interface GoogleVerifyTokenPayloadDto {
  id: string;
  email: string;
  name?: string;
  picture?: string;
}

@Injectable()
export class GoogleVerifyTokenService {
  constructor(private readonly configService: ConfigService) {}

  async validate(token: string): Promise<GoogleVerifyTokenPayloadDto> {
    try {
      const response = await fetch(
        `https://oauth2.googleapis.com/tokeninfo?id_token=${token}`,
      );

      if (!response.ok || response.status !== 200) {
        throw new Error('Invalid Google token');
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const data = await response.json();
      const payload = data as GoogleVerifyTokenPayload;

      return {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
      };
    } catch (error) {
      throw new Error('Failed to validate Google token', { cause: error });
    }
  }
}
