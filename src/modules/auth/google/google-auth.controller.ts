import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthResponseDto } from '../dtos/auth.dto';

import { ApiProperty } from '@nestjs/swagger';
import { GoogleAuthService } from './google-auth.service';

export class GoogleAuthDto {
  @ApiProperty()
  token: string;

  @ApiProperty()
  data?: {
    email: string;
    name: string;
  };
}

@ApiTags('Auth')
@Controller('auth')
export class GoogleAuthController {
  private readonly logger = new Logger(GoogleAuthController.name);
  constructor(private readonly googleAuthService: GoogleAuthService) {}

  @Post('google')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sign in with Google' })
  @ApiResponse({
    status: HttpStatus.OK,
    type: AuthResponseDto,
    description: 'Successfully authenticated',
  })
  async googleLogin(@Body() { token }: GoogleAuthDto): Promise<any> {
    try {
      return await this.googleAuthService.loginWithGoogle(token);
    } catch (error) {
      this.logger.error('Failed to login with google', error);
      return new InternalServerErrorException(error);
    }
  }
}
