import { Injectable, Logger } from '@nestjs/common';

import { UsersService } from '@/modules/users/services/users.service';
import { JwtService } from '@nestjs/jwt';
import { v4 } from 'uuid';
import { GoogleVerifyTokenService } from './google-verify-token.service';

@Injectable()
export class GoogleAuthService {
  private readonly logger = new Logger(GoogleAuthService.name);

  constructor(
    private readonly googleVerifyTokenService: GoogleVerifyTokenService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async loginWithGoogle(token: string): Promise<any> {
    try {
      const googlePayload = await this.googleVerifyTokenService.validate(token);

      const user = await this.usersService.findOrCreateSocialUser({
        provider: 'google',
        providerId: googlePayload.id,
        email: googlePayload.email,
        name: googlePayload.name,
        picture: googlePayload.picture,
      });

      const accessToken = await this.jwtService.signAsync({
        sub: user.externalId,
        role: user.roles,
        permissions: user.permissions,
        jti: v4(),
      });

      return {
        accessToken,
        user: {
          id: user.externalId,
          email: user.email,
          name: user.name.displayName,
          picture: user.picture,
        },
      };
    } catch (error) {
      this.logger.error('Failed to login with Google', error);
      throw error;
    }
  }
}
