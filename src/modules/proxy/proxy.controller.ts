import { All, <PERSON>, Req, Res } from '@nestjs/common';
import type { FastifyReply, FastifyRequest } from 'fastify';

declare module 'fastify' {
  interface FastifyReply {
    from(url: string, options?: Record<string, unknown>): void;
  }
}

@Controller('d')
export class ProxyController {
  private readonly targetBase: string;
  constructor() {
    this.targetBase = 'https://audiobot.pro';
  }

  @All('p/*')
  forward(@Req() req: FastifyRequest, @Res() reply: FastifyReply) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const path = req.params['*'] as string;
    const url = `${this.targetBase}/${path}${req.url.includes('?') ? req.url.slice(req.url.indexOf('?')) : ''}`;

    return reply.from(url, {});
  }
}
