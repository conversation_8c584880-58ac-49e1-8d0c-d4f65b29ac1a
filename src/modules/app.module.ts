import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Joi from 'joi';
import { AppController } from './app.controller';
import { CustomLogger } from '@/common/services/logger.service';
import { AuthModule } from './auth/auth.module';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from './users/users.module';
import { ClsModule } from 'nestjs-cls';
import { CacheModule } from '@nestjs/cache-manager';
import { WineAssistantModule } from './wine-assistant/wine-assistant.module';
import KeyvRedis, { createClient, Keyv } from '@keyv/redis';
import { ProxyModule } from './proxy/proxy.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      validationOptions: {
        abortEarly: true,
      },
      validationSchema: Joi.object({
        PORT: Joi.number().default(3000),
        MONGODB_URI: Joi.string().required(),
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
        LOG_LEVEL: Joi.string()
          .valid('fatal', 'error', 'warn', 'info', 'debug', 'trace')
          .default('info'),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRES_IN: Joi.string().default('1d'),
      }),
      envFilePath: [`.env.${process.env.NODE_ENV}`, '.env', '.env.local'],
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        const redisClient: any = createClient({
          socket: {
            host: configService.getOrThrow('REDIS_SERVER_HOST'),
            port: configService.getOrThrow('REDIS_SERVER_PORT'),
            connectTimeout: 5000,
            keepAlive: 60000,
            noDelay: true,
          },
          disableOfflineQueue: false,
          password: configService.getOrThrow('REDIS_SERVER_PASSWORD'),
        });

        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        redisClient.on('connect', () => {
          console.log('Redis client connected');
        });

        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        redisClient.on('ready', () => {
          console.log('Redis client ready');
        });

        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        await redisClient.connect().catch((err) => {
          console.log(err);
        });

        const store = new Keyv({
          store: new KeyvRedis(redisClient),
          namespace: 'memici',
          useKeyPrefix: false,
        });

        store.on('err', () => {});

        return {
          stores: [store],
        };
      },
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        uri: config.getOrThrow<string>('MONGODB_URI'),
      }),
    }),
    AuthModule,
    ClsModule.forRoot({
      global: true,
      middleware: { mount: true },
    }),
    UsersModule,
    WineAssistantModule,
    ProxyModule,
  ],
  controllers: [AppController],
  providers: [CustomLogger],
})
export class AppModule {}
