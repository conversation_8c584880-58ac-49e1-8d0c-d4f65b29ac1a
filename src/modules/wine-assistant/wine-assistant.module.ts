import { Module } from '@nestjs/common';
import { WineAssistantController } from './controllers/wine-assistant.controller';
import { AuthModule } from '../auth/auth.module';
import { AskAiHandler } from './handlers/ask-ai.handler';
import { OpenAiService } from './infrastructure/openai/openai.service';

@Module({
  imports: [AuthModule],
  controllers: [WineAssistantController],
  providers: [AskAiHandler, OpenAiService],
})
export class WineAssistantModule {}
