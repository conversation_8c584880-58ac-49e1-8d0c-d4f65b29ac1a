import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OpenAI } from 'openai';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';

const ResponseSchema = z.object({
  name: z.string().min(1),
  country: z.string(),
  price: z.string(),
  grapeVariety: z.string(),
  description: z.string(),
});

const jsonSchema = zodToJsonSchema(ResponseSchema, 'ResponseSchema');

@Injectable()
export class OpenAiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenAiService.name);

  constructor(private readonly config: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.config.get<string>('OPENAI_API_KEY'),
      project: 'memici',
      organization: 'memici-llc',
    });
  }

  async askAssistant(prompt: string): Promise<string> {
    const systemMsg = `
      You are a Master Sommelier AI with 20+ years of professional tasting experience.
      You only discuss wine: its selection, tasting notes, pairing, serving, storage, recommendations.
      If the user asks anything outside this domain, you MUST respond with valid JSON:
        { "error": "Out of scope. I only handle wine recommendations." }

        1. For each wine:
          • name, region, vintage, grapeVariety  
          • flavorProfile: { aroma, body, acidity, tannin, finish }  
          • servingTemperature (°C)  
          • decantingTime (minutes)  
          • priceRange (USD)  
          • recommendedGlassware  

        2. Analyze them comparatively and produce a “finalRecommendation” object that:
          • rates each wine on a 100-point scale  
          • highlights the top choice(s) for the user’s occasion  
          • gives a brief justification (max 100 chars each)  

      Respond with valid JSON only—no code fences, no extra text.  
      Use metric units. Keep each string under 100 characters.  
      The response must validate against this JSON Schema:  
      ${JSON.stringify(jsonSchema)}
    `.trim();

    const res = await this.openai.chat.completions.create({
      model: 'gpt-4',
      temperature: 0.0,
      messages: [
        { role: 'system', content: systemMsg },
        { role: 'user', content: prompt },
      ],
      functions: [
        {
          name: 'handle_response',
          description: 'Structured response',
          parameters: jsonSchema,
        },
      ],

      function_call: { name: 'handle_response' },
    });
    return res.choices[0].message.content ?? '';
  }
}
