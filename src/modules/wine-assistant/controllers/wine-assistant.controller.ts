import {
  Controller,
  Post,
  Body,
  UseGuards,
  InternalServerErrorException,
} from '@nestjs/common';
import { AskAiDto } from '@/modules/wine-assistant/interfaces/dto/ask-ai.dto';
import { JwtAuthGuard } from '@/modules/auth/jwt-auth.guard';
import { Ask<PERSON>iHandler } from '@/modules/wine-assistant/handlers/ask-ai.handler';

@UseGuards(JwtAuthGuard)
@Controller('wine-assistant')
export class WineAssistantController {
  constructor(private readonly askAi: AskAiHandler) {}

  @Post('ask')
  async ask(@Body() dto: AskAiDto) {
    try {
      const response = await this.askAi.execute(dto.prompt);
      return { response };
    } catch (err) {
      return new InternalServerErrorException(err);
    }
  }
}
