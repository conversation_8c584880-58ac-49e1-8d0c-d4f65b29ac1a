import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { AppModule } from '@/modules/app.module';
// import helmet from '@fastify/helmet';
// import { fastifyCompress } from '@fastify/compress';
import { CustomLogger } from './common/services/logger.service';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { VersioningType } from '@nestjs/common';
import { fastifyReplyFrom } from '@fastify/reply-from';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      trustProxy: true,
      logger: true,
    }),
    { bufferLogs: true },
  );

  const logger = app.get(CustomLogger);
  app.useLogger(logger);

  // await app.register(fastifyCompress, { threshold: 512 });

  app.useGlobalFilters(new GlobalExceptionFilter());

  await app.register(fastifyReplyFrom);

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
    prefix: 'v',
  });

  const config = new DocumentBuilder()
    .setTitle('Memici API')
    .setDescription('API')
    .setVersion('1.0')
    .addServer('https://api.memici.com', 'Production')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  SwaggerModule.setup('openapi', app, document, {
    jsonDocumentUrl: '/openapi.json',
  });

  await app.listen(process.env.PORT ?? 3000, () => {
    process.send?.('ready');
  });
}
bootstrap().catch(() => {
  process.exit(1);
});
