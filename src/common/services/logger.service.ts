import { Inject, Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import pino from 'pino';

@Injectable()
export class CustomLogger implements LoggerService {
  private logger: pino.Logger;

  constructor(
    @Inject(ConfigService) private readonly configService: ConfigService,
  ) {
    const logLevel =
      this.configService.get<
        'fatal' | 'error' | 'warn' | 'info' | 'debug' | 'trace'
      >('LOG_LEVEL') ?? 'info';
    const isProduction = this.configService.get('NODE_ENV') === 'production';

    this.logger = pino({
      level: logLevel,
      transport: isProduction
        ? undefined
        : {
            target: 'pino-pretty',
            options: {
              colorize: true,
            },
          },
      timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
    });

    this.logger.info(
      {
        env: this.configService.get<string>('NODE_ENV'),
        logLevel: logLevel,
      },
      'Logger initialized',
    );
  }

  log(message: string, context?: string) {
    this.logger.info({ context }, message);
  }

  error(message: string, trace?: string, context?: string) {
    this.logger.error({ context, trace }, message);
  }

  warn(message: string, context?: string) {
    this.logger.warn({ context }, message);
  }

  debug(message: string, context?: string) {
    this.logger.debug({ context }, message);
  }

  verbose(message: string, context?: string) {
    this.logger.trace({ context }, message);
  }
}
