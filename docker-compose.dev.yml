version: '2.4'

services:
  redis:
    image: redis:latest
    container_name: redis
    command: redis-server --requirepass 112233
    ports:
      - '6379:6379'
    environment:
      - REDIS_PASSWORD=112233
    volumes:
      - redis-data:/data
    networks:
      - redisnet

  # use host.docker.internal:6379 to connect to the host machine
  redisinsights:
    image: redislabs/redisinsight:latest
    container_name: redisinsights
    ports:
      - '5540:5540'
    volumes:
      - 'redisinsights:/data'
    networks:
      - redisnet
    depends_on:
      - redis

  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger
    command:
      - '--query.base-path=/jaeger/ui'
    ports:
      - '16686:16686'
      - '4318:4318'
      - '4317:4317'
      - '9411:9411'
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # OpenTelemetry Collector
  otelcol:
    image: otel/opentelemetry-collector-contrib:0.88.0
    container_name: otel-col
    ports:
      - '4317' # OTLP over gRPC receiver
      - '4318' # OTLP over HTTP receiver
    command: ['--config=/etc/otelcol-config.yml']
    volumes:
      - ./docker/otelcollector/otelcol-config.yml:/etc/otelcol-config.yml
    depends_on:
      - jaeger

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - '9090:9090'
volumes:
  redis-data:
  redisinsights:

networks:
  redisnet:
