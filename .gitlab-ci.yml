image: ubuntu:22.04

stages:
  - test
  - build
  - deploy
  - dast

variables:
  NODE_VERSION: '22'
  PM2_VERSION: 'latest'
  DEPLOY_KEY_PATH: '/root/.ssh/id_rsa'
  GITLAB_ADVANCED_SAST_ENABLED: 'true'
  DS_MAX_DEPTH: '-1'
  TELEGRAM_BOT_TOKEN: '$TELEGRAM_BOT_TOKEN'
  TELEGRAM_CHAT_ID: '-1002639088336'
  PROJECT_NAME: 'memici-api'

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/

test:
  stage: test
  variables:
    NODE_ENV: 'test'
  script:
    - apt-get update -y && apt-get install -y curl
    - curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
    - apt-get install -y nodejs
    - npm ci
    - npx eslint "{src,apps,libs,test}/**/*.ts" --fix
    - npm run test
  artifacts:
    paths:
      - coverage/
    expire_in: 1 week

build:
  stage: build
  script:
    - apt-get update -y && apt-get install -y curl
    - curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
    - apt-get install -y nodejs
    - npm ci
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

deploy-prod:
  stage: deploy
  environment:
    name: production
    url: https://api.memici.com
  when: manual
  dependencies:
    - build

  before_script:
    - apt-get update -y && apt-get install -y openssh-client git curl
    - curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
    - apt-get install -y nodejs
    - npm install -g pm2@$PM2_VERSION
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > $DEPLOY_KEY_PATH
    - chmod 600 $DEPLOY_KEY_PATH
    - ssh-keyscan -H $TARGET_SERVER_HOST > ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - eval $(ssh-agent -s)
    - ssh-add $DEPLOY_KEY_PATH

  script:
    - set -e
    - echo "Starting deployment to production..."
    - ssh $TARGET_SERVER_USER@$TARGET_SERVER_HOST "echo 'MONGODB_URI=$MONGODB_URI' > /home/<USER>/apps/memici-api/current/.env"
    - ssh $TARGET_SERVER_USER@$TARGET_SERVER_HOST "echo 'PORT=$PORT' >> /home/<USER>/apps/memici-api/current/.env"
    - ssh $TARGET_SERVER_USER@$TARGET_SERVER_HOST "echo 'NODE_ENV=$NODE_ENV' >> /home/<USER>/apps/memici-api/current/.env"
    - if [ "$CI_COMMIT_REF_NAME" == "$CI_DEFAULT_BRANCH" ]; then
      pm2 deploy ecosystem.config.cjs production setup || true;
      pm2 deploy ecosystem.config.cjs production;
      fi;

  after_script:
    - rm -f $DEPLOY_KEY_PATH
    - |
      if [ "$CI_JOB_STATUS" == "success" ]; then
        curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
          -d chat_id="$TELEGRAM_CHAT_ID" \
          -d parse_mode="Markdown" \
          -d text="✅ *Deploy Succeeded*  
            *Project*: $PROJECT_NAME  
            *Commit*: [$CI_COMMIT_SHA]($CI_PROJECT_URL/-/commit/$CI_COMMIT_SHA)  
            *Branch*: $CI_COMMIT_REF_NAME  
            *URL*: $CI_ENVIRONMENT_URL  
            *Time*: $(TZ='Europe/Moscow' date '+%d-%m-%Y %H:%M:%S MSK')";
      elif [ "$CI_JOB_STATUS" == "failed" ]; then
        curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
          -d chat_id="$TELEGRAM_CHAT_ID" \
          -d parse_mode="Markdown" \
          -d text="❌ *Deploy Failed*  
            *Project*: $PROJECT_NAME  
            *Commit*: [$CI_COMMIT_SHA]($CI_PROJECT_URL/-/commit/$CI_COMMIT_SHA)  
            *Branch*: $CI_COMMIT_REF_NAME";
      fi

  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

include:
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Dependency-Scanning.gitlab-ci.yml
